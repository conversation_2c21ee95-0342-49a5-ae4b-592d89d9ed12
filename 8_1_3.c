﻿/*********************************************************************************
// This code is created by SimCoder Version 2025a15.147 for F28004x Hardware Target
//
// SimCoder is copyright by Powersim Inc., 2009-2021
//
// Date: August 01, 2025 19:28:01
**********************************************************************************/
#include	<math.h>
#include	"PS_bios.h"
#define	GetCurTime() PS_GetSysTimer()
#define	iif(a, b, c) ((a) ? (b) : (c))
typedef interrupt void (*ClaIntr)(void);
ClaIntr Cla1Task1 = 0;
ClaIntr Cla1Task2 = 0;
ClaIntr Cla1Task3 = 0;
ClaIntr Cla1Task4 = 0;
ClaIntr Cla1Task5 = 0;
ClaIntr Cla1Task6 = 0;
ClaIntr Cla1Task7 = 0;
ClaIntr Cla1Task8 = 0;




interrupt void Task();

#ifdef _FLASH
#pragma DATA_SECTION(PSK_SysClk, "copysections")
#endif
const Uint16 PSK_SysClk = 100;  // MHz
extern	DefaultType	fGblVphase;
extern	DefaultType	fGblVod;
extern	DefaultType	fGblVoq;
extern	DefaultType	fGblIod;
extern	DefaultType	fGblIoq;
extern	DefaultType	fGblVcona;
extern	DefaultType	fGblVconb;
extern	DefaultType	fGblVconc;
extern	DefaultType	fGblIodc;
extern	DefaultType	fGblIoqc;
extern	DefaultType	fGblVodc;
extern	DefaultType	fGblUDELAY15;
extern	DefaultType	fGblUDELAY3;
extern	DefaultType	fGblUDELAY16;
extern	DefaultType	fGblUDELAY17;
extern	DefaultType	fGblVoqc;
extern	DefaultType	fGblsIoa;
extern	DefaultType	fGblVq;
extern	DefaultType	fGblVd;
extern	DefaultType	fGblVd1;
extern	DefaultType	fGblVd2;
extern	DefaultType	fGblUDELAY4;


#define	PSM_VRefHiA		3.3		// ADC-A VREFHIA
#define	PSM_VRefHiB		3.3		// ADC-B VREFHIB
#define	PSM_VRefHiC		3.3		// ADC-C VREFHIC





PST_BufItem aGblSciOutBuf[32];
PST_Data aGblSciInValue[3] = {0, 0, 0};
Uint16 aGblSciOutAllow[3] = {0,0,0};
Uint16 aGblSciOutCnt[3] = {0,0,0};
Uint16 nGblSciState = 0;
Uint16 aGblSciDateSetPt[3] = {1,1,1};
char* aGblSciInitStr = "\0016,1:PSM_Vdc=10000\003\0016,2:PSM_Vref=10000\003\0016,3:PSM_Ila=10000\003\0011,1:SCI_RunStop=0\003\0011,2:SCI_Freq=0\003\0011,3:SCI_VoltRef=0\003";
#define	PSC_SCI_SENDOUT_FLAG	0x2000
#define	PSC_SCI_INITIAL		0
#define	PSC_SCI_START		0x5000000
#define	PSC_SCI_PAUSE		0x1000000
#define	PSC_SCI_RESTART		0x2000000
#define	PSC_SCI_CONT_MODE	0x3000000
#define	PSC_SCI_SNAP_MODE	0x4000000
#define	PSC_SCI_CONT_START	1
#define	PSC_SCI_CONT_BEGIN	2
#define	PSC_SCI_CONT_SEND	3
#define	PSC_SCI_CONT_PAUSE	4
#define	PSC_SCI_SNAP_START	100
#define	PSC_SCI_SNAP_BEGIN	101
#define	PSC_SCI_SNAP_SEND	102
#define	PSC_SCI_SNAP_WAIT	103
#define	PSC_SCI_SNAP_PSEND	104
#define	PSC_SCI_SNAP_PWAIT	105
#define	PSC_SCI_SNAP_PAUSE	106
void _ProcSciInputItem(PST_BufItem* pItem)
{
	Uint16 i, nSeqNo = pItem->nSeqNo.bit.nSeqNo;
	switch (nSeqNo) {
	case 0:
		switch (pItem->data.dataInt32) {
		case PSC_SCI_INITIAL:
			for (i = 0; i < 3; i++) aGblSciOutAllow[i] = 0;
			PS_SciClearSendBuf();
			PS_SciSendInitStr(aGblSciInitStr);
			break;
		case PSC_SCI_PAUSE:
			PSM_DisableIntr();
			switch (nGblSciState) {
			case PSC_SCI_CONT_START:
			case PSC_SCI_CONT_SEND:
				PS_SciClearSendBuf();
				nGblSciState = PSC_SCI_CONT_PAUSE;
				break;
			case PSC_SCI_SNAP_SEND:
				nGblSciState = PSC_SCI_SNAP_PSEND;
				break;
			case PSC_SCI_SNAP_WAIT:
				nGblSciState = PSC_SCI_SNAP_PWAIT;
				break;
			default:
				break;
			}
			PSM_EnableIntr();
			break;
		case PSC_SCI_RESTART:
			PSM_DisableIntr();
			switch (nGblSciState) {
			case PSC_SCI_CONT_PAUSE:
				nGblSciState = PSC_SCI_CONT_START;
				break;
			case PSC_SCI_SNAP_PSEND:
			case PSC_SCI_SNAP_PWAIT:
			case PSC_SCI_SNAP_PAUSE:
				nGblSciState = PSC_SCI_SNAP_START;
				break;
			}
			PSM_EnableIntr();
			break;
		case PSC_SCI_CONT_MODE:
			nGblSciState = PSC_SCI_CONT_START;
			break;
		case PSC_SCI_SNAP_MODE:
			nGblSciState = PSC_SCI_SNAP_START;
			break;
		default:
			if (pItem->nSeqNo.bit.nCount == 0) {
				for (i = 0; i < 3; i++) aGblSciOutAllow[i] = 0;
			}
			for (i = 0; i < 4; i++) {
				int index = (pItem->data.dataInt32 >> (i * 8)) & 0xff;
				if ((index > 0) && (index <= 3))
					aGblSciOutAllow[index - 1] = PSC_SCI_SENDOUT_FLAG;
			}
			break;
		}
		break;
	default:
		if (nSeqNo <= 3)
			aGblSciInValue[nSeqNo - 1].dataInt32 = pItem->data.dataInt32;
		break;
	}
}

void _ProcSciRestart(void)
{
	int i;
	PST_BufItem item;

	for (i = 0; i < 3; i++)
		aGblSciOutAllow[i] &= 0xff00;
	item.nSeqNo.all = 0;
	switch (nGblSciState++) {
	case PSC_SCI_CONT_BEGIN:
		PS_SciClearSendBuf();
		item.data.dataInt32 = 0;
		break;
	case PSC_SCI_SNAP_BEGIN:
		item.data.dataInt32 = 1;
		break;
	case PSC_SCI_SNAP_PWAIT:
		nGblSciState = PSC_SCI_SNAP_START;
	case PSC_SCI_SNAP_WAIT:
		item.data.dataInt32 = 255;
		break;
	}
	PS_SciSendItem(&item);
}

void _ProcSciWaitStart(void)
{
	PSM_DisableIntr();
	switch (nGblSciState) {
	case PSC_SCI_CONT_START:
		nGblSciState = PSC_SCI_CONT_BEGIN;
		break;
	case PSC_SCI_SNAP_START:
		nGblSciState = PSC_SCI_SNAP_BEGIN;
		break;
	default:
		break;
	}
	PSM_EnableIntr();
}

void _ProcSciOutput(int index, float fVal)
{
	PST_BufItem item;
	int ok = ((aGblSciOutAllow[index] & PSC_SCI_SENDOUT_FLAG) &&
		(++aGblSciOutCnt[index] >= aGblSciDateSetPt[index]));
	PSM_DisableIntr();
	switch (nGblSciState) {
	case PSC_SCI_CONT_BEGIN:
	case PSC_SCI_SNAP_BEGIN:
		_ProcSciRestart();
		break;
	case PSC_SCI_CONT_SEND:
		if (ok) {
			aGblSciOutCnt[index] = 0;
			item.nSeqNo.bit.nCount = aGblSciOutAllow[index];
			item.nSeqNo.bit.nSeqNo = index + 1;
			item.data.dataFloat = fVal;
			PS_SciSendItem(&item);
			aGblSciOutAllow[index]++;
			aGblSciOutAllow[index] &= ~0x100;
		}
		break;
	case PSC_SCI_SNAP_SEND:
	case PSC_SCI_SNAP_PSEND:
		if (ok) {
			aGblSciOutCnt[index] = 0;
			item.nSeqNo.bit.nCount = aGblSciOutAllow[index];
			item.nSeqNo.bit.nSeqNo = index + 1;
			item.data.dataFloat = fVal;
			if (!PS_SciSendItem(&item)) {
				nGblSciState++;
			} else {
				aGblSciOutAllow[index]++;
				aGblSciOutAllow[index] &= ~0x100;
			}
		}
		break;
	case PSC_SCI_SNAP_WAIT:
		if (PS_IsTxQueueEmpty()) {
			nGblSciState = PSC_SCI_SNAP_START;
		}
		break;
	case PSC_SCI_SNAP_PWAIT:
		if (PS_IsTxQueueEmpty()) {
			nGblSciState = PSC_SCI_SNAP_PAUSE;
		}
		break;
	default:
		break;
	}
	PSM_EnableIntr();
}

DefaultType	fGblVphase = 0;
DefaultType	fGblVod = 0;
DefaultType	fGblVoq = 0;
DefaultType	fGblIod = 0;
DefaultType	fGblIoq = 0;
DefaultType	fGblVcona = 0;
DefaultType	fGblVconb = 0;
DefaultType	fGblVconc = 0;
DefaultType	fGblIodc = 0;
DefaultType	fGblIoqc = 0;
DefaultType	fGblVodc = 0;
DefaultType	fGblUDELAY15 = 0;
DefaultType	fGblUDELAY3 = 0;
DefaultType	fGblUDELAY16 = 0;
DefaultType	fGblUDELAY17 = 0;
DefaultType	fGblVoqc = 0;
DefaultType	fGblsIoa = 0;
DefaultType	fGblVq = 0;
DefaultType	fGblVd = 0;
DefaultType	fGblVd1 = 0;
DefaultType	fGblVd2 = 0;
DefaultType	fGblUDELAY4 = 0;
interrupt void Task()
{
	DefaultType	fUDELAY4, fMULT14, fZOH49, fMUX4A1, fVSQ3, fVSQ1, fNOT1, fLIM17;
	DefaultType	fLIM16, fLIM15, fZOH39, fZOH38, fZOH22, fPSM_F28004x_ADC2_6;
	DefaultType	fZOH37, fUDELAY17, fMULT11, fUDELAY16, fMULT10, fUDELAY3, fMULT9;
	DefaultType	fUDELAY15, fMULT1, fSUMP28, fP52, fSUMP27, fP51, fSUMP9, fP49;
	DefaultType	fSUMP39, fMAX_MIN2, fMAX_MIN4, fMAX_MIN1, fMAX_MIN3, fP50, fDQO1_2;
	DefaultType	fDQO1_1, fDQO1, fLIM29, fDIVD2, fSUMP29, fSUMP24, fP32, fLIM30;
	DefaultType	fSUMP16, fLIM26, fSUMP17, fP22, fP21, fSUM10, fSUMP15, fSUMP26;
	DefaultType	fP34, fLIM25, fSUMP13, fLIM22, fSUMP14, fP20, fP19, fSUM9, fLIM28;
	DefaultType	fSUMP31, fLIM23, fSUMP18, fC13, fLIM34, fSCI_VoltRef, fLIM18;
	DefaultType	fDIVD1, fP38, fZOH40, fPSM_F28004x_ADC2_2, fSUMP25, fSUM11;
	DefaultType	fP29, fLIM31, fSUMP22, fLIM19, fSUMP23, fP28, fP27, fSUM13;
	DefaultType	fABC4_2, fABC4_1, fABC4, fSUM22, fSUM21, fC11, fP37, fZOH21;
	DefaultType	fSUMP57, fPSM_F28004x_ADC2_4, fP30, fZOH36, fSUMP59, fPSM_F28004x_ADC1_8;
	DefaultType	fSUMP21, fABC3_2, fABC3_1, fABC3, fSUM20, fP36, fZOH19, fSUMP55;
	DefaultType	fPSM_F28004x_ADC1_1, fSUM14, fC10, fP35, fZOH18, fSUMP54, fPSM_F28004x_ADC1_3;
	DefaultType	fSUM15, fP31, fLIM20, fSUMP19, fLIM21, fSUMP20, fP26, fP25;
	DefaultType	fSUM12, fABC1_2, fABC1_1, fABC1, fI_RESET_I_D2, fMULT12, fAND3;
	DefaultType	fF004x_DIN1, fLIM32, fSUMP32, fC6, fSCI_RunStop, fP17, fSUMP30;
	DefaultType	fLIM33, fSCI_Freq, fC5, fFCNM5, fFCNM4, fP40, fZOH9, fSUMP52;
	DefaultType	fPSM_F28004x_ADC1, fFCNM3, fP41, fZOH8, fSUMP51, fPSM_F28004x_ADC1_4;
	DefaultType	fP39, fZOH10, fSUMP53, fVDC14, fPSM_F28004x_ADC1_2, fC9;

	ADC_CLR(2) = 1 << (1-1);	// Clear ADC interrupt flag 1
	CPU_PIEACK |= M__INT1;
	fUDELAY15 = fGblUDELAY15;

	fUDELAY3 = fGblUDELAY3;

	fUDELAY16 = fGblUDELAY16;

	fUDELAY17 = fGblUDELAY17;

	fUDELAY4 = fGblUDELAY4;


	fC9 = 0;
	fPSM_F28004x_ADC1_2 = ADC_RESULT(2, 2) * (1.0 * PSM_VRefHiC / 4096.0);
	fVDC14 = (-1.65);
	fSUMP53 = fPSM_F28004x_ADC1_2 + fVDC14;
	fZOH10 = fSUMP53;
	fP39 = fZOH10 * 33.57;
	fPSM_F28004x_ADC1_4 = ADC_RESULT(2, 4) * (1.0 * PSM_VRefHiC / 4096.0);
	fSUMP51 = fPSM_F28004x_ADC1_4 + fVDC14;
	fZOH8 = fSUMP51;
	fP41 = fZOH8 * 33.57;
	fFCNM3 = (fP39-fP41)/3.0;
	fPSM_F28004x_ADC1 = ADC_RESULT(2, 0) * (1.0 * PSM_VRefHiC / 4096.0);
	fSUMP52 = fPSM_F28004x_ADC1 + fVDC14;
	fZOH9 = fSUMP52;
	fP40 = fZOH9 * 33.57;
	fFCNM4 = (fP40-fP39)/3.0;
	fFCNM5 = (fP41-fP40)/3.0;
	fC5 = 50;
	fSCI_Freq = aGblSciInValue[1].dataFloat;
	fLIM33 = (fSCI_Freq > 50) ? 50 : ((fSCI_Freq < (-30)) ? (-30) : fSCI_Freq);
	fSUMP30 = fC5 + fLIM33;
	fP17 = fSUMP30 * (2.0*3.1415926);
	fSCI_RunStop = aGblSciInValue[0].dataFloat;
	fC6 = 1;
	fSUMP32 = fSCI_RunStop + fC6;
	fLIM32 = (fSUMP32 > 1) ? 1 : ((fSUMP32 < 0) ? 0 : fSUMP32);
	fF004x_DIN1 = PSM_GpioGetInput(10);
	fAND3 = (fLIM32 > 0.3) && (fF004x_DIN1 > 0.3);
	fMULT12 = fP17 * fAND3;
	{
		static DefaultType out_A = 0;
		static DefaultType in_A = 0.0;
		fI_RESET_I_D2 = out_A + 0.5/10000 * (fMULT12 + in_A);
		if (fI_RESET_I_D2 > (2.0*3.1415926)) {
			fI_RESET_I_D2 -= (2.0*3.1415926) - 0;
		} else if (fI_RESET_I_D2 < 0) {
			fI_RESET_I_D2 += (2.0*3.1415926) - 0;
		}
		out_A = fI_RESET_I_D2; in_A = fMULT12;
	}
#ifdef	_DEBUG
	fGblVphase = fI_RESET_I_D2;
#endif

	// ABC to DQ transformation
	fABC1 = 2.0/3.0 * (cos(fI_RESET_I_D2) * fFCNM3 + cos(fI_RESET_I_D2-2*3.14159265/3) * fFCNM4 + cos(fI_RESET_I_D2+2*3.14159265/3) * fFCNM5);
	fABC1_1 = 2.0/3.0 * (sin(fI_RESET_I_D2) * fFCNM3 + sin(fI_RESET_I_D2-2*3.14159265/3) * fFCNM4 + sin(fI_RESET_I_D2+2*3.14159265/3) * fFCNM5);
	fABC1_2 = (fFCNM3 + fFCNM4 + fFCNM5) / 3.0;
#ifdef	_DEBUG
	fGblVod = fABC1;
#endif

#ifdef	_DEBUG
	fGblVoq = fABC1_1;
#endif

	fSUM12 = fC9 - fABC1;
	fP25 = fSUM12 * 0.5;
	fP26 = fSUM12 * 0.01;
	fSUMP20 = fP26 + fUDELAY15;
	fLIM21 = (fSUMP20 > 5) ? 5 : ((fSUMP20 < (-5)) ? (-5) : fSUMP20);
	fSUMP19 = fP25 + fLIM21;
	fLIM20 = (fSUMP19 > 20) ? 20 : ((fSUMP19 < (-20)) ? (-20) : fSUMP19);
	fP31 = fABC1_1 * (314.0*10.0E-6);
	fSUM15 = fLIM20 - fP31;
	fPSM_F28004x_ADC1_3 = ADC_RESULT(2, 3) * (1.0 * PSM_VRefHiC / 4096.0);
	fSUMP54 = fPSM_F28004x_ADC1_3 + fVDC14;
	fZOH18 = fSUMP54;
	fP35 = fZOH18 * 3.788;
	fC10 = 0;
	fSUM14 = fC10 - fP35;
	fPSM_F28004x_ADC1_1 = ADC_RESULT(2, 1) * (1.0 * PSM_VRefHiC / 4096.0);
	fSUMP55 = fPSM_F28004x_ADC1_1 + fVDC14;
	fZOH19 = fSUMP55;
	fP36 = fZOH19 * 3.788;
	fSUM20 = fSUM14 - fP36;
	// ABC to DQ transformation
	fABC3 = 2.0/3.0 * (cos(fI_RESET_I_D2) * fP35 + cos(fI_RESET_I_D2-2*3.14159265/3) * fSUM20 + cos(fI_RESET_I_D2+2*3.14159265/3) * fP36);
	fABC3_1 = 2.0/3.0 * (sin(fI_RESET_I_D2) * fP35 + sin(fI_RESET_I_D2-2*3.14159265/3) * fSUM20 + sin(fI_RESET_I_D2+2*3.14159265/3) * fP36);
	fABC3_2 = (fP35 + fSUM20 + fP36) / 3.0;
	fSUMP21 = fSUM15 + fABC3;
	fPSM_F28004x_ADC1_8 = ADC_RESULT(2, 5) * (1.0 * PSM_VRefHiC / 4096.0);
	fSUMP59 = fPSM_F28004x_ADC1_8 + fVDC14;
	fZOH36 = fSUMP59;
	fP30 = fZOH36 * 3.788;
	fPSM_F28004x_ADC2_4 = ADC_RESULT(0, 1) * (1.0 * PSM_VRefHiA / 4096.0);
	fSUMP57 = fPSM_F28004x_ADC2_4 + fVDC14;
	fZOH21 = fSUMP57;
	fP37 = fZOH21 * 3.788;
	fC11 = 0;
	fSUM21 = fC11 - fP30;
	fSUM22 = fSUM21 - fP37;
	// ABC to DQ transformation
	fABC4 = 2.0/3.0 * (cos(fI_RESET_I_D2) * fP30 + cos(fI_RESET_I_D2-2*3.14159265/3) * fP37 + cos(fI_RESET_I_D2+2*3.14159265/3) * fSUM22);
	fABC4_1 = 2.0/3.0 * (sin(fI_RESET_I_D2) * fP30 + sin(fI_RESET_I_D2-2*3.14159265/3) * fP37 + sin(fI_RESET_I_D2+2*3.14159265/3) * fSUM22);
	fABC4_2 = (fP30 + fP37 + fSUM22) / 3.0;
#ifdef	_DEBUG
	fGblIod = fABC4;
#endif

#ifdef	_DEBUG
	fGblIoq = fABC4_1;
#endif

	fSUM13 = fSUMP21 - fABC4;
	fP27 = fSUM13;
	fP28 = fSUM13 * 0.001;
	fSUMP23 = fP28 + fUDELAY17;
	fLIM19 = (fSUMP23 > 20) ? 20 : ((fSUMP23 < (-20)) ? (-20) : fSUMP23);
	fSUMP22 = fP27 + fLIM19;
	fLIM31 = (fSUMP22 > 20) ? 20 : ((fSUMP22 < (-20)) ? (-20) : fSUMP22);
	fP29 = fABC4_1 * (314.0*1.0E-3);
	fSUM11 = fLIM31 - fP29;
	fSUMP25 = fSUM11 + fABC1;
	fPSM_F28004x_ADC2_2 = ADC_RESULT(0, 0) * (1.0 * PSM_VRefHiA / 4096.0);
	fZOH40 = fPSM_F28004x_ADC2_2;
	fP38 = fZOH40 * 33.57;
	fDIVD1 = fSUMP25 / fP38;
	fLIM18 = (fDIVD1 > 1) ? 1 : ((fDIVD1 < (-1)) ? (-1) : fDIVD1);
	fSCI_VoltRef = aGblSciInValue[2].dataFloat;
	fLIM34 = (fSCI_VoltRef > 5) ? 5 : ((fSCI_VoltRef < (-32)) ? (-32) : fSCI_VoltRef);
	fC13 = 0.01;
	fSUMP18 = fC13 + fUDELAY4;
	fLIM23 = (fSUMP18 > 32) ? 32 : ((fSUMP18 < 0) ? 0 : fSUMP18);
	fSUMP31 = fLIM34 + fLIM23;
	fLIM28 = (fSUMP31 > 40) ? 40 : ((fSUMP31 < 0) ? 0 : fSUMP31);
	fSUM9 = fLIM28 - fABC1_1;
	fP19 = fSUM9 * 0.5;
	fP20 = fSUM9 * 0.01;
	fSUMP14 = fP20 + fUDELAY3;
	fLIM22 = (fSUMP14 > 5) ? 5 : ((fSUMP14 < (-5)) ? (-5) : fSUMP14);
	fSUMP13 = fP19 + fLIM22;
	fLIM25 = (fSUMP13 > 20) ? 20 : ((fSUMP13 < (-20)) ? (-20) : fSUMP13);
	fP34 = fABC1 * (314.0*10.0E-6);
	fSUMP26 = fLIM25 + fP34;
	fSUMP15 = fSUMP26 + fABC3_1;
	fSUM10 = fSUMP15 - fABC4_1;
	fP21 = fSUM10;
	fP22 = fSUM10 * 0.001;
	fSUMP17 = fP22 + fUDELAY16;
	fLIM26 = (fSUMP17 > 20) ? 20 : ((fSUMP17 < (-20)) ? (-20) : fSUMP17);
	fSUMP16 = fP21 + fLIM26;
	fLIM30 = (fSUMP16 > 20) ? 20 : ((fSUMP16 < (-20)) ? (-20) : fSUMP16);
	fP32 = fABC4 * (314.0*1.0E-3);
	fSUMP24 = fLIM30 + fP32;
	fSUMP29 = fSUMP24 + fABC1_1;
	fDIVD2 = fSUMP29 / fP38;
	fLIM29 = (fDIVD2 > 1) ? 1 : ((fDIVD2 < (-1)) ? (-1) : fDIVD2);
	// DQ to ABC transformation
	fDQO1 = cos(fI_RESET_I_D2) * fLIM18 + sin(fI_RESET_I_D2) * fLIM29 + 0;
	fDQO1_1 = cos(fI_RESET_I_D2 - 2*3.14159265/3) * fLIM18 + sin(fI_RESET_I_D2 - 2*3.14159265/3) * fLIM29 + 0;
	fDQO1_2 = cos(fI_RESET_I_D2 + 2*3.14159265/3) * fLIM18 + sin(fI_RESET_I_D2 + 2*3.14159265/3) * fLIM29 + 0;
	fP50 = fDQO1;
fMAX_MIN3 = (fDQO1 < fDQO1_1) ? fDQO1_1 : fDQO1;
fMAX_MIN1 = (fMAX_MIN3 < fDQO1_2) ? fDQO1_2 : fMAX_MIN3;
fMAX_MIN4 = (fDQO1 > fDQO1_1) ? fDQO1_1 : fDQO1;
fMAX_MIN2 = (fDQO1_2 > fMAX_MIN4) ? fMAX_MIN4 : fDQO1_2;
	fSUMP39 = fMAX_MIN1 + fMAX_MIN2;
	fP49 = fSUMP39 * (-0.5);
	fSUMP9 = fP50 + fP49;
#ifdef	_DEBUG
	fGblVcona = fSUMP9;
#endif
	fP51 = fDQO1_1;
	fSUMP27 = fP51 + fP49;
#ifdef	_DEBUG
	fGblVconb = fSUMP27;
#endif
	fP52 = fDQO1_2;
	fSUMP28 = fP52 + fP49;
#ifdef	_DEBUG
	fGblVconc = fSUMP28;
#endif
#ifdef	_DEBUG
	fGblIodc = fSUMP19;
#endif
#ifdef	_DEBUG
	fGblIoqc = fSUMP13;
#endif
#ifdef	_DEBUG
	fGblVodc = fC9;
#endif
	fMULT1 = fLIM21 * fAND3;
	fGblUDELAY15 = fMULT1;
	fMULT9 = fLIM22 * fAND3;
	fGblUDELAY3 = fMULT9;
	fMULT10 = fLIM26 * fAND3;
	fGblUDELAY16 = fMULT10;
	fMULT11 = fLIM19 * fAND3;
	fGblUDELAY17 = fMULT11;
	fZOH37 = fP38;
	fPSM_F28004x_ADC2_6 = ADC_RESULT(0, 2) * (1.0 * PSM_VRefHiA / 4096.0);
	fZOH22 = fPSM_F28004x_ADC2_6;
	fZOH38 = fZOH22;
	fZOH39 = fP35;
	fLIM15 = (fSUMP9 > 1) ? 1 : ((fSUMP9 < (-1)) ? (-1) : fSUMP9);
	fLIM16 = (fSUMP27 > 1) ? 1 : ((fSUMP27 < (-1)) ? (-1) : fSUMP27);
	fLIM17 = (fSUMP28 > 1) ? 1 : ((fSUMP28 < (-1)) ? (-1) : fSUMP28);
	if (fAND3 > 0.3)
	{
		PSM_Pwm3phStart(101-100);
	}
	fNOT1 = (fAND3 <= 0.3) ? 1 : 0;
	if (fNOT1 > 0.3)
	{
		PSM_Pwm3phStop(101-100);
	}
#ifdef	_DEBUG
	fGblVoqc = fLIM28;
#endif
	{
		static DefaultType wt = 1.0 - (0 / 360.);
		static DefaultType dwt = 0.5 * 1.0 / 10000;
		fVSQ1 = (wt < 0.5) ? ((1) + (0)) : (0);
		wt += dwt;
		if (wt >= 1.0)
			wt -= 1.0;
	}
	{
		static DefaultType wt = 1.0 - (0 / 360.);
		static DefaultType dwt = 2 * 1.0 / 10000;
		fVSQ3 = (wt < 0.5) ? ((1) + (0)) : (0);
		wt += dwt;
		if (wt >= 1.0)
			wt -= 1.0;
	}
	switch ((int)(fAND3 + 0.5)) {
	case 0:
		fMUX4A1 = fVSQ1;
		break;
	case 1:
		fMUX4A1 = fVSQ3;
		break;
	case 2:
		fMUX4A1 = 0;
		break;
	default:
		fMUX4A1 = 0;
		break;
	}
	fZOH49 = fMUX4A1;
	PSM_GpioSetOutput(6, fZOH49 > 0.3 ? 1 : 0);
#ifdef	_DEBUG
	fGblsIoa = fP30;
#endif
#ifdef	_DEBUG
	fGblVq = fDIVD2;
#endif
#ifdef	_DEBUG
	fGblVd = fDIVD1;
#endif
#ifdef	_DEBUG
	fGblVd1 = fSUMP22;
#endif
#ifdef	_DEBUG
	fGblVd2 = fSUMP16;
#endif
	fMULT14 = fLIM23 * fAND3;
	fGblUDELAY4 = fMULT14;
	// Start of changing PWM3ph1 registers
	// Set Duty Cycle of U
	PWM_CMPA(1) = PWM_TBPRD(1) * (__fsat(fLIM15, 1 + (-0.5), (-0.5)) - (-0.5)) * (1.0/1);
	PWM_CMPA(2) = PWM_TBPRD(1) * (__fsat(fLIM16, 1 + (-0.5), (-0.5)) - (-0.5)) * (1.0/1);
	PWM_CMPA(3) = PWM_TBPRD(1) * (__fsat(fLIM17, 1 + (-0.5), (-0.5)) - (-0.5)) * (1.0/1);
	// End of changing PWM3ph1 registers
	if (nGblSciState != PSC_SCI_INITIAL) {
		_ProcSciOutput(0, fZOH37);
		_ProcSciOutput(1, fZOH38);
		_ProcSciOutput(2, fZOH39);
	}
}


void Initialize(void)
{
	PS_SysInit(2, 20);
	PS_PwmStartStopClock(0);	// Stop Pwm Clock
	PS_TimerInit(0, 0);

	// Set initial states for those GPIO output ports.
	PSM_GpioSetOutput(6, 0);	// Reset GPIO6
	PS_GpioSetFunc(6, 0, eSync1Samp, eGpioOutPullup, 0);	// Initialize GPIO6
	PS_SetVREF(0, 1, 0);	// Set external VRef for ADC-A
	PS_SetVREF(1, 1, 0);	// Set external VRef for ADC-B
	PS_SetVREF(2, 1, 0);	// Set external VRef for ADC-C

	{
	    int i, preAdcNo = -1;
	    /* PST_AdcAttr: Adc No., Channel No., Soc No., Trig Src, SampleTime(clock) */
	    const PST_AdcAttr aryAdcInit[9] = {
			{2, 0, 0, ADCTRIG_PWM1, 32},
			{2, 1, 1, ADCTRIG_PWM1, 32},
			{2, 2, 2, ADCTRIG_PWM1, 32},
			{2, 3, 3, ADCTRIG_PWM1, 32},
			{2, 4, 4, ADCTRIG_PWM1, 32},
			{2, 8, 5, ADCTRIG_PWM1, 32},
			{0, 2, 0, ADCTRIG_PWM1, 32},
			{0, 4, 1, ADCTRIG_PWM1, 32},
			{0, 6, 2, ADCTRIG_PWM1, 32}};
	    const PST_AdcAttr *p = aryAdcInit;
	    for (i = 0; i < 9; i++, p++) {
	        if (preAdcNo != p->nAdcNo) {
	            PS_AdcInit(p->nAdcNo);
	            preAdcNo = p->nAdcNo;
	        }
	        PS_AdcSetChn(p->nAdcNo, p->nChnNo, p->nSocNo, p->nTrigSrc, p->nWindSz);
	    }
	}

	PS_Pwm3phInit(1, 0, 1, 1.e6/(10000*1.0), ePwmStartHigh1, ePwmComplement, HRPWM_DISABLE);	// pwmNo, seqNo, wave type, period, PwmA, PWMB, UseHRPwm
	PS_PwmSetDeadBand(1, 0, 2, 3, 0, 1, 1);
	PS_PwmSetDeadBand(2, 0, 2, 3, 0, 1, 1);
	PS_PwmSetDeadBand(3, 0, 2, 3, 0, 1, 1);
	PS_PwmSetIntrType(1, ePwmIntrAdc, 1, 0.5);
	PS_AdcSetIntr(2, 1, 5, Task); // AdcNo, IntrNo, SocNo, Interrupt Vector
	PS_PwmSetTripAction(1, eTzHiZ, eTzHiZ);
	PS_PwmSetTripAction(2, eTzHiZ, eTzHiZ);
	PS_PwmSetTripAction(3, eTzHiZ, eTzHiZ);
	PWM_CMPA(1) = (0 - (-0.5)) / (1.0 * 1) * PWM_TBPRD(1);
	PWM_CMPA(2) = (0 - (-0.5)) / (1.0 * 1) * PWM_TBPRD(1);
	PWM_CMPA(3) = (0 - (-0.5)) / (1.0 * 1) * PWM_TBPRD(1);
	PSM_Pwm3phStop(1);       // Stop Pwm3ph1 at the beginning

	PS_GpioSetFunc(10, 0, eSync1Samp, eGpioIn, 0);

	PS_GpioSetFunc(6, 0, eSync1Samp, eGpioOutPullup, 0);

	PS_SciInit(13, 12, 115200, 0, aGblSciOutBuf, 32, &_ProcSciInputItem);	// Rx(GPIO13), Tx(GPIO12)

	PS_PwmStartStopClock(1);	// Start Pwm Clock
}


void main()
{
	Initialize();
	PSM_EnableIntr();   // Enable Global interrupt INTM
	PSM_EnableDbgm();
	for (;;) {
		_ProcSciWaitStart();
	}
}

